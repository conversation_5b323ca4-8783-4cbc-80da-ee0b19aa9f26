extends CharacterBody3D

# Movimento e Sensibilidade
@export_group("Movement & Sensitivity")
@export var speed := 5.0
@export var gravity := 9.8
@export var mouse_sensitivity := 0.002

# Limites de Rotação da Câmera
@export_group("Camera Look Limits")
@export var max_look_up_deg := 80.0  # Ângulo máximo para olhar para cima em graus
@export var max_look_down_deg := 80.0  # Ângulo máximo para olhar para baixo em graus

# Prevenção de Colisão da Câmera com o Chão
@export_group("Camera Floor Collision Prevention")
@export var enable_camera_floor_prevention := true # Habilita/desabilita a prevenção
@export var camera_floor_ray_length := 1.5 # Comprimento do raio para checar o chão abaixo da câmera
@export var camera_min_distance_from_floor := 0.5 # Distância mínima que a câmera deve manter do chão
# Limite mais restritivo para olhar para baixo quando o chão está muito próximo (deve ser <= -max_look_down_deg)
@export var camera_forced_look_down_limit_deg := -70.0

@onready var head: Node3D = $Head
@onready var camera: Camera3D = $Head/Camera3D

var rotation_x_rad := 0.0 # Rotação vertical da cabeça/câmera em radianos

func _ready() -> void:
	Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)

func _input(event: InputEvent) -> void:
	if event is InputEventMouseMotion:
		# Rotação horizontal do corpo do jogador
		rotate_y(-event.relative.x * mouse_sensitivity)

		# Rotação vertical da cabeça/câmera
		var new_rotation_x_rad = rotation_x_rad - event.relative.y * mouse_sensitivity
		
		# Aplica os limites normais de olhar para cima/baixo
		new_rotation_x_rad = clamp(new_rotation_x_rad,
			deg_to_rad(-max_look_down_deg), deg_to_rad(max_look_up_deg))

		# Lógica adicional para prevenir a câmera de atravessar o chão
		if enable_camera_floor_prevention and new_rotation_x_rad < 0: # Apenas se olhando para baixo
			# Calcula a posição global e direção da câmera ANTES de aplicar a nova rotação vertical
			# para ter uma base estável para o raycast.
			# Temporariamente aplica a rotação para obter a direção correta do raycast.
			var original_head_rotation_x = head.rotation.x
			head.rotation.x = new_rotation_x_rad
			
			var camera_global_transform: Transform3D = camera.global_transform
			var camera_global_pos: Vector3 = camera_global_transform.origin
			# A direção para baixo da câmera (relativo ao mundo)
			var look_down_direction: Vector3 = -camera_global_transform.basis.z 
			
			# Restaura a rotação original da cabeça para o cálculo da física
			head.rotation.x = original_head_rotation_x

			var space_state: PhysicsDirectSpaceState3D = get_world_3d().direct_space_state
			var query := PhysicsRayQueryParameters3D.create(
				camera_global_pos,
				camera_global_pos + look_down_direction * camera_floor_ray_length
			)
			query.exclude = [self] # Exclui o próprio jogador da colisão do raio
			
			var result: Dictionary = space_state.intersect_ray(query)

			if result:
				var distance_to_hit: float = camera_global_pos.distance_to(result.position)
				if distance_to_hit < camera_min_distance_from_floor:
					# Se o chão está muito perto, aplica um limite mais restritivo para olhar para baixo
					new_rotation_x_rad = max(new_rotation_x_rad, deg_to_rad(camera_forced_look_down_limit_deg))
		
		rotation_x_rad = new_rotation_x_rad
		head.rotation.x = rotation_x_rad

func _physics_process(delta: float) -> void:
	# Aplica gravidade
	if not is_on_floor():
		velocity.y -= gravity * delta
	else:
		# Pequeno impulso para baixo para garantir que is_on_floor() funcione de forma consistente
		# se o jogador estiver em uma ladeira, por exemplo. Pode ser ajustado ou removido.
		velocity.y = -0.1 

	# Captura input de movimento
	var input_dir: Vector2 = Input.get_vector("move_left", "move_right", "move_forward", "move_back")
	# Transforma a direção do input para o espaço local do jogador e normaliza
	var direction: Vector3 = (transform.basis * Vector3(input_dir.x, 0, input_dir.y)).normalized()

	# Aplica movimento
	if direction != Vector3.ZERO:
		velocity.x = direction.x * speed
		velocity.z = direction.z * speed
	else:
		# Desaceleração suave
		velocity.x = move_toward(velocity.x, 0, speed * delta * 10.0) # Ajuste o multiplicador para a "suavidade"
		velocity.z = move_toward(velocity.z, 0, speed * delta * 10.0)

	move_and_slide()
