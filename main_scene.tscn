[gd_scene load_steps=12 format=3 uid="uid://oy6wtm3xoqe4"]

[ext_resource type="Script" uid="uid://c4hbkrfhg6nep" path="res://player.gd" id="1_2c62f"]
[ext_resource type="AudioStream" uid="uid://kjxjffo1w528" path="res://audio_segment.mp3" id="1_d5x1h"]
[ext_resource type="Material" uid="uid://c0dihndvf23e1" path="res://star_sky.tres" id="1_hq1ik"]
[ext_resource type="Script" uid="uid://5kgqgmmnyxos" path="res://textbox.gd" id="3_gyfs4"]
[ext_resource type="PackedScene" uid="uid://dch8claey6wmr" path="res://assets/models/Player/Model/characterMedium.fbx" id="4_nfk4m"]

[sub_resource type="Sky" id="Sky_66tet"]
sky_material = ExtResource("1_hq1ik")

[sub_resource type="Environment" id="Environment_d5x1h"]
background_mode = 2
sky = SubResource("Sky_66tet")

[sub_resource type="CameraAttributesPhysical" id="CameraAttributesPhysical_8mm7a"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_d5x1h"]

[sub_resource type="PlaneMesh" id="PlaneMesh_wr18j"]

[sub_resource type="BoxShape3D" id="BoxShape3D_hq1ik"]
size = Vector3(20, 0.01, 20)

[node name="Node3D" type="Node3D"]

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 5.22651, 0)

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_d5x1h")
camera_attributes = SubResource("CameraAttributesPhysical_8mm7a")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]

[node name="AudioStreamPlayer3D" type="AudioStreamPlayer3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 9.58699, 0)
stream = ExtResource("1_d5x1h")
autoplay = true
playback_type = 1

[node name="CharacterBody3D" type="CharacterBody3D" parent="."]
script = ExtResource("1_2c62f")

[node name="CollisionShape3D" type="CollisionShape3D" parent="CharacterBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00126886, 2.12897, 0.00250643)
shape = SubResource("CapsuleShape3D_d5x1h")

[node name="characterMedium" parent="CharacterBody3D" instance=ExtResource("4_nfk4m")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 0, 0)

[node name="Head" type="Node3D" parent="CharacterBody3D"]

[node name="Camera3D" type="Camera3D" parent="CharacterBody3D/Head"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 3.4, 0.3)

[node name="MeshInstance3D" type="MeshInstance3D" parent="CharacterBody3D"]

[node name="CanvasLayer" type="CanvasLayer" parent="."]
script = ExtResource("3_gyfs4")

[node name="Label" type="Label" parent="CanvasLayer"]
offset_right = 40.0
offset_bottom = 23.0

[node name="Ground" type="StaticBody3D" parent="."]

[node name="MeshInstance3D" type="MeshInstance3D" parent="Ground"]
transform = Transform3D(10, 0, 0, 0, 10, 0, 0, 0, 10, 0, 0, 0)
mesh = SubResource("PlaneMesh_wr18j")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Ground"]
shape = SubResource("BoxShape3D_hq1ik")
